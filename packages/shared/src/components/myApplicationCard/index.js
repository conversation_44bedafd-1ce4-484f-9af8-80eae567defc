import { Badge, Text } from '@libs/components'
import { useTheme } from '@libs/theme'
import { SecureStore } from '@libs/utils'
import { isMobile, isWeb } from '@libs/utils/src/screenLayout'
import { useIsFocused, useNavigation } from '@react-navigation/native'
import { t } from 'i18next'
import React, { useEffect, useState } from 'react'
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native'
import { formatIntakeDate } from '@libs/utils/src/dateformat'
import { MyApplicationMobile } from './index.mobile'
import { badgeColor, excludedDurationBrands } from '../../constants'
import { formattedStatus } from '../../utils/formattedStatus'

export const MyApplication = ({ data, style, disabled }) => {
  const isDesktop = isWeb(useWindowDimensions().width)
  const [brandLogoUrl, setBrandLogoUrl] = useState('')
  const { colors } = useTheme()
  const navigation = useNavigation()
  const isMobileScreen = isMobile(useWindowDimensions().width)

  const handleNavigation = () => {
    if (data?.ApplicationStatus?.category === 'Drafts') {
      navigation.navigate('new-oap-application', {
        id: data.Id,
      })
      return
    }
    navigation.navigate('view-application-details', {
      id: data.Id,
      isActive:
        data?.ActionRequiredCount > 0 ? 'ACTION_REQUIRED' : 'INFORMATION',
      isDashboard: true,
    })
  }

  const isFocused = useIsFocused()

  const shouldRenderProcessingCard =
    data?.ApplicationStatus?.status === 'Drafts' &&
    data?.isSubmittedFromAppHero === true

  const duration = data?.OpportunityLineItems?.records[0]?.Product2?.Duration__c
  let opportunitiesDetails = [
    {
      label: t('APPLICATION_DETAIL.INTAKE'),
      value: data.IntakeDate ? formatIntakeDate(data.IntakeDate) : '-',
      separate: false,
    },
    {
      value: duration || '-',
      label: t('APPLICATION_DETAIL.DURATION'),
      separate: false,
    },
    {
      label: t('APPLICATION_DETAIL.LOCATION'),
      value:
        data?.Location__c ??
        data?.OpportunityLineItems?.records?.[0]?.Location__c ??
        '-',
      separate: false,
    },
  ]

  if (excludedDurationBrands.includes(data.BusinessUnitFilter__c)) {
    opportunitiesDetails = opportunitiesDetails.filter(
      (item) => item.label !== t('APPLICATION_DETAIL.DURATION'),
    )
  }

  useEffect(() => {
    if (!isFocused) return
    ;(async () => {
      const configData = await SecureStore.getItemAsync('config')
      const config = JSON.parse(configData)?.config
      setBrandLogoUrl(config?.brandLogoUrl)
    })()
  }, [isFocused])

  const programName = data?.ProgrammeName__c
    ? `${data?.ProgrammeName__c}${
        data?.Delivery_Mode__c ? `, ${data?.Delivery_Mode__c}` : ''
      }`
    : ''

  // Render the processing card design when conditions are met
  if (shouldRenderProcessingCard) {
    return (
      <View
        style={[
          styles.mainContainer,
          { backgroundColor: '#DDE0EE', padding: isMobileScreen ? 24 : 30 },
        ]}
      >
        <View
          style={{
            flexDirection: isMobileScreen ? 'column' : 'row',
            alignItems: isMobileScreen ? 'flex-start' : 'center',
            justifyContent: 'space-between',
            paddingBottom: 8,
            flexWrap: 'wrap',
          }}
        >
          <Image
            source={`${brandLogoUrl}/${data.BusinessUnitFilter__c?.replaceAll(
              ' ',
              '_',
            )}.png`}
            style={{
              height: 55,
              width: 126,
            }}
            alt={data?.brand}
            resizeMode="contain"
          />
          <View
            style={{
              flex: 1,
              alignSelf: 'center',
            }}
          >
            <Text
              style={{
                fontWeight: 700,
                fontSize: 16,
                color: colors.cardTitleColor,
                paddingRight: 24,
              }}
            >
              {programName || data?.Name?.split('_')[0]}
            </Text>
          </View>
        </View>

        {/* Processing Note Section */}
        <View
          style={{
            backgroundColor: '#F5F5F5',
            borderRadius: 8,
            paddingHorizontal: isMobileScreen ? 16 : 32,
            paddingVertical: isMobileScreen ? 12 : 16,
            flexDirection: 'row',
            alignItems: 'flex-start',
            marginTop: !isMobileScreen ? 0 : 12,
          }}
        >
          <View
            style={{
              backgroundColor: '#FF8C00',
              borderRadius: 20,
              width: 40,
              height: 40,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
            }}
          >
            <svg
              width="42"
              height="43"
              viewBox="0 0 42 43"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="21" cy="21.2656" r="21" fill="#D4691B" />
              <path
                d="M23 11.2656H15C14.4696 11.2656 13.9609 11.4763 13.5858 11.8514C13.2107 12.2265 13 12.7352 13 13.2656V29.2656C13 29.7961 13.2107 30.3048 13.5858 30.6798C13.9609 31.0549 14.4696 31.2656 15 31.2656H27C27.5304 31.2656 28.0391 31.0549 28.4142 30.6798C28.7893 30.3048 29 29.7961 29 29.2656V17.2656L23 11.2656Z"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M23 11.2656V17.2656H29"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M25 22.2656H17"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M25 26.2656H17"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M19 18.2656H18H17"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </View>
          <View style={{ flex: 1 }}>
            <Text
              style={{
                fontWeight: 700,
                fontSize: 16,
                color: '#D4691B',
                marginBottom: 4,
              }}
            >
              Note:
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: '#030229',
                lineHeight: 20,
                fontWeight: 400,
              }}
            >
              Your Application is processing right now. Please refresh after a
              short while to see application details!
            </Text>
          </View>
        </View>
      </View>
    )
  }

  if (isDesktop) {
    return (
      <TouchableOpacity
        style={[styles.mainContainer, style]}
        onPress={() => handleNavigation()}
        disabled={disabled}
      >
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingBottom: 8,
            flexWrap: !isDesktop ? 'wrap' : 'nowrap',
          }}
        >
          <Image
            source={`${brandLogoUrl}/${data.BusinessUnitFilter__c?.replaceAll(
              ' ',
              '_',
            )}.png`}
            style={{
              height: 55,
              width: 126,
            }}
            alt={data?.brand}
            resizeMode="contain"
          />
          {isDesktop ? (
            <View
              style={{
                justifyContent: 'flex-start',
                flex: 1,
                alignSelf: 'baseline',
              }}
            >
              <Text
                style={{
                  fontWeight: 700,
                  fontSize: 16,
                  color: colors.cardTitleColor,
                  paddingRight: 20,
                }}
              >
                {programName ? programName : data?.Name?.split('_')[0]}
              </Text>

              <View
                style={{
                  flexDirection: 'row',
                  marginTop: 10,
                  gap: 4,
                  flexWrap: 'wrap',
                }}
              >
                {opportunitiesDetails?.slice(0, 3).map((item, index) => (
                  <View
                    key={index}
                    style={{ flexDirection: 'row', alignItems: 'center' }}
                  >
                    <Text variant="display4" color={colors.textSecondary}>
                      <Text variant="display4" color={colors.onNeutral}>
                        {item.label}:{' '}
                      </Text>
                      {item.value}
                    </Text>
                    {opportunitiesDetails.length - 1 !== index && (
                      <View style={styles.divider(colors)} />
                    )}
                  </View>
                ))}
              </View>
            </View>
          ) : null}

          <View
            style={{
              justifyContent: 'flex-start',
              alignSelf: isDesktop ? 'baseline' : 'center',
              flexGrow: 0,
            }}
          >
            <Badge
              style={[
                badgeColor[data?.ApplicationStatus?.status.split(' ')[0]] ||
                  badgeColor[data?.ApplicationStatus?.status.split('/')[0]],
              ]}
              textVariant="display4"
            >
              Status : {formattedStatus(data?.ApplicationStatus?.status)}
            </Badge>
          </View>
        </View>
      </TouchableOpacity>
    )
  }
  return (
    <MyApplicationMobile
      data={data}
      style={style}
      disabled={disabled}
      shouldRenderProcessingCard={shouldRenderProcessingCard}
      isMobileScreen={isMobileScreen}
    />
  )
}

const styles = StyleSheet.create({
  mainContainer: {
    borderRadius: 10,
    backgroundColor: '#fff',
    paddingHorizontal: 30,
    paddingVertical: 30,
  },
  data: () => ({
    flexDirection: window.innerWidth >= 376 ? 'row' : 'column',
    flexWrap: 'wrap',
    gap: window.innerWidth <= 376 ? 6 : 0,
  }),
  divider: (colors) => ({
    backgroundColor: colors.onNeutral,
    width: 1,
    height: '100%',
    marginHorizontal: 15,
  }),
  buttonStyleOutlined: (colors, isDesktop) => ({
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: isDesktop ? 40 : 20,
    paddingVertical: 10,
    borderColor: colors.primaryContainer,
  }),
  buttonStyleFilled: (isDesktop) => ({
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: isDesktop ? 40 : 20,
    paddingVertical: 10,
  }),
})
