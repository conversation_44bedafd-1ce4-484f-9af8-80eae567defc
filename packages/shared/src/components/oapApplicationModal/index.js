import { View, StyleSheet, useWindowDimensions } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Text } from '@libs/components'
import { generateOapAuthPayload } from '../../utils'
import OapPortalMobile from './index.mobile'

const OapPortal = ({
  portalUrl,
  messagePayload,
  onClose = () => {},
  opportunityDetails,
  userType = 'student',
  applicationStatus = 'new',
  userEmail,
  tokens,
  handleSubmissionConfirmation = () => {},
}) => {
  const { t } = useTranslation()
  const iframeRef = useRef(null)
  const { width } = useWindowDimensions()
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [isIframeReady, setIsIframeReady] = useState(false)

  useEffect(() => {
    const sendTokensToIframe = async () => {
      try {
        setIsLoading(true)
        setIsIframeReady(false)

        localStorage.setItem(
          'redirect-to-oap',
          JSON.stringify({
            enable: true,
            oapName: messagePayload?.oapName,
          }),
        )

        setIsLoading(false)

        // Get the iframe element
        const iframe = document.getElementById('oap-portal-iframe')
        if (!iframe) {
          setErrorMessage('Cannot find Oap Portal iframe. Please try again.')
          setIsLoading(false)
          return
        }

        const targetOrigin = new URL(portalUrl).origin

        // Function to send authentication message to iframe
        const sendAuthMessage = () => {
          // Use the dynamic payload generation utility
          let message

          if (opportunityDetails && userEmail && tokens) {
            // Generate dynamic payload based on user type and status
            try {
              message = generateOapAuthPayload(
                opportunityDetails,
                userType,
                applicationStatus,
                tokens,
                userEmail,
              )
            } catch (error) {
              console.warn(
                'Failed to generate dynamic payload, falling back to messagePayload:',
                error,
              )
              message = messagePayload
            }
          }

          console.log('Sending message to OAP Portal:', targetOrigin)

          if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage(message, targetOrigin)
            setIsLoading(false)
          } else {
            setErrorMessage('Cannot access Oap Portal. Please try again.')
            setIsLoading(false)
          }
        }

        const messageHandler = (event) => {
          // Process messages from the iframe
          if (event.data && typeof event.data === 'object') {
            if (event.data.type === 'ready_state') {
              setIsIframeReady(true)
              sendAuthMessage()
            }
          }
        }

        window.addEventListener('message', messageHandler)

        // Add a load event listener to the iframe
        const iframeLoadHandler = () => {
          const readyTimeout = setTimeout(() => {
            if (!isIframeReady) {
              sendAuthMessage()
            }
          }, 6000)

          iframe.readyTimeoutId = readyTimeout

          // Remove the event listener after it's been triggered
          iframe.removeEventListener('load', iframeLoadHandler)
        }

        // Add the load event listener to the iframe
        iframe.addEventListener('load', iframeLoadHandler)

        // If the iframe is already loaded, trigger the handler manually
        if (iframe.contentDocument) {
          iframeLoadHandler()
        }
      } catch (error) {
        setErrorMessage(`Failed to initialize Oap Portal: ${error.message}`)
        setIsLoading(false)
      }
    }

    sendTokensToIframe()

    return () => {
      const iframe = document.getElementById('oap-portal-iframe')
      if (iframe) {
        iframe.removeEventListener('load', () => {})
        // Clear timeout if it exists
        if (iframe.readyTimeoutId) {
          clearTimeout(iframe.readyTimeoutId)
        }
      }
      // Remove message listener
      window.removeEventListener('message', () => {})
    }
  }, [messagePayload, portalUrl, isIframeReady, tokens])

  useEffect(() => {
    // Set the iframe height to fill the container
    const iframe = document.getElementById('oap-portal-iframe')
    if (iframe.contentDocument) {
      window.addEventListener('message', (event) => {
        if (event.data.type === 'SUBMISSION_CONFIRMATION') {
          handleSubmissionConfirmation(event.data)
        }
      })
    }
  }, [])

  // Use mobile version for smaller screens
  if (width < 650) {
    return (
      <OapPortalMobile
        portalUrl={portalUrl}
        messagePayload={messagePayload}
        onClose={onClose}
        opportunityDetails={opportunityDetails}
        userType={userType}
        applicationStatus={applicationStatus}
        userEmail={userEmail}
        tokens={tokens}
        handleSubmissionConfirmation={handleSubmissionConfirmation}
      />
    )
  }

  return (
    <View style={styles.container}>
      {(() => {
        if (isLoading) {
          return (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading Oap Portal...</Text>
            </View>
          )
        }
        if (errorMessage) {
          return (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{errorMessage}</Text>
              <Button
                label={t('Oap_PORTAL.CLOSE', 'Close')}
                onPress={onClose}
                buttonStyle={styles.errorButton}
              />
            </View>
          )
        }
        return (
          <View style={styles.iframeContainer}>
            <iframe
              id="oap-portal-iframe"
              ref={iframeRef}
              src={`${portalUrl}?fromAppHero=true`}
              style={styles.iframe}
              title="Oap Portal"
            />
          </View>
        )
      })()}
    </View>
  )
}

const styles = StyleSheet.create({
  content: {
    width: '100%',
    height: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  iframeContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  iframe: {
    width: '100%',
    height: '100%',
    border: 'none',
    borderRadius: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    marginBottom: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
  },
  errorButton: {
    marginTop: 20,
    minWidth: 120,
  },
  container: {
    flex: 1,
    minHeight: '80vh',
  },
})

export default OapPortal
